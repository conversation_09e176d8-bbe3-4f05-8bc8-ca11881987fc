using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

public static class WeatherServer
{
    private const int Port = 8080;
    
    public static async Task Main(string[] args)
    {
        var listener = new TcpListener(IPAddress.Any, Port);
        listener.Start();
        Console.WriteLine($"Weather MCP Server started on port {Port}");
        
        while (true)
        {
            try
            {
                var client = await listener.AcceptTcpClientAsync();
                _ = HandleClientAsync(client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accepting client: {ex.Message}");
            }
        }
    }
    
    private static async Task HandleClientAsync(TcpClient client)
    {
        try
        {
            using (client)
            using (var stream = client.GetStream())
            {
                var buffer = new byte[1024];
                var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                var requestJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                
                // Parse coordinates from request
                var request = JsonSerializer.Deserialize<WeatherRequest>(requestJson);
                Console.WriteLine($"Received request for coordinates: {request?.Latitude}, {request?.Longitude}");
                
                // Create response (always the same)
                var response = new WeatherResponse
                {
                    Weather = "it is snowing in San Diego",
                    Temperature = -2.5
                };
                
                var responseJson = JsonSerializer.Serialize(response);
                var responseBytes = Encoding.UTF8.GetBytes(responseJson);
                await stream.WriteAsync(responseBytes, 0, responseBytes.Length);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
    }
    
    private class WeatherRequest
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }
    
    private class WeatherResponse
    {
        public string Weather { get; set; }
        public double Temperature { get; set; }
    }
}