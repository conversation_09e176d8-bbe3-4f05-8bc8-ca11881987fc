# Weather MCP Server

A simple C# TCP server that provides weather information via the Model Context Protocol (MCP). This server listens on port 8080 and responds to weather requests with mock weather data.

## Features

- TCP server listening on port 8080
- Accepts JSON requests with latitude and longitude coordinates
- Returns mock weather data (always reports "it is snowing in San Diego" with temperature -2.5°C)
- Asynchronous client handling

## Prerequisites

- .NET 6.0 or later
- C# compiler (included with .NET SDK)

## Getting Started

### 1. Build and Run the Server

#### Option A: Run as a Script (Simplest)

Since this is a single C# file, you can run it directly:

```bash
# Navigate to the project directory
cd c:\dev\master3\MCP\Weather

# Run the C# file as a script (requires .NET 6+)
dotnet script WeatherServer.cs
```

If `dotnet script` is not available, use Option B below.

#### Option B: Create a Console Project (Recommended)

```bash
# Navigate to the project directory
cd c:\dev\master3\MCP\Weather

# Create a new console project
dotnet new console -n WeatherServer

# Replace the default Program.cs with our WeatherServer.cs content
copy WeatherServer.cs WeatherServer\Program.cs

# Navigate to the project directory
cd WeatherServer

# Build and run
dotnet build
dotnet run
```

#### Option C: Compile Standalone File

```bash
# Compile the single file directly
csc WeatherServer.cs

# Run the executable
WeatherServer.exe
```

### 2. Server Output

When the server starts successfully, you should see:

```
Weather MCP Server started on port 8080
```

The server will continue running and log incoming requests:

```
Received request for coordinates: 37.7749, -122.4194
```

## API Usage

### Request Format

Send a JSON request to the server on port 8080 with the following format:

```json
{
  "Latitude": 37.7749,
  "Longitude": -122.4194
}
```

### Response Format

The server will respond with:

```json
{
  "Weather": "it is snowing in San Diego",
  "Temperature": -2.5
}
```

### Example with curl

```bash
# Test the server
echo '{"Latitude": 37.7749, "Longitude": -122.4194}' | nc localhost 8080
```

## Adding to MCP Configuration

To integrate this weather server with an MCP client, add the following configuration to your MCP JSON configuration file:

### Option 1: Using Console Project (Recommended)

First, create the console project as described in the Getting Started section, then use:

```json
{
  "mcpServers": {
    "weather": {
      "command": "dotnet",
      "args": ["run"],
      "cwd": "c:\\dev\\master3\\MCP\\Weather\\WeatherServer",
      "transport": {
        "type": "tcp",
        "host": "localhost",
        "port": 8080
      }
    }
  }
}
```

### Option 2: Using Compiled DLL

After creating the console project and building it:

```json
{
  "mcpServers": {
    "weather": {
      "command": "dotnet",
      "args": ["WeatherServer.dll"],
      "cwd": "c:\\dev\\master3\\MCP\\Weather\\WeatherServer\\bin\\Debug\\net6.0",
      "transport": {
        "type": "tcp",
        "host": "localhost",
        "port": 8080
      }
    }
  }
}
```

### Option 3: Standalone Executable

For a self-contained deployment:

```bash
# Publish as standalone executable
dotnet publish -c Release -r win-x64 --self-contained
```

Configuration:

```json
{
  "mcpServers": {
    "weather": {
      "command": "c:\\dev\\master3\\MCP\\Weather\\bin\\Release\\net6.0\\win-x64\\publish\\WeatherServer.exe",
      "transport": {
        "type": "tcp",
        "host": "localhost",
        "port": 8080
      }
    }
  }
}
```

## Configuration Notes

- **Port**: The server is hardcoded to use port 8080. Ensure this port is available.
- **Host**: The server binds to all interfaces (`IPAddress.Any`), so it accepts connections from any IP.
- **Transport**: This server uses TCP transport, not stdio, so make sure your MCP client supports TCP connections.

## Troubleshooting

### Common Issues

1. **Port already in use**: If port 8080 is occupied, you'll need to modify the `Port` constant in `WeatherServer.cs`.

2. **Firewall blocking**: Ensure Windows Firewall allows the application to accept incoming connections.

3. **MCP client connection issues**: Verify that your MCP client supports TCP transport and is configured with the correct host and port.

### Logs

The server outputs logs to the console, including:
- Server startup confirmation
- Incoming request coordinates
- Error messages for connection or parsing issues

## Development

To modify the weather data or add new features:

1. Edit the `WeatherResponse` creation in the `HandleClientAsync` method
2. Add new properties to the `WeatherRequest` and `WeatherResponse` classes as needed
3. Implement actual weather API integration if desired

## License

This is a simple example server for demonstration purposes.
